package iploc

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/ip2location/ip2location-go/v9"
	"net"
)

// TODO 错误处理： init时如果报错，error log， 查ip时如果报错，把错误返回出去
// 通用的ip地址库， 使用 ip2location 的离线数据库

var ipv4db *ip2location.DB
var ipv6db *ip2location.DB

const (
	defaultIPv4DB = "./resource/ipdb/IP2LOCATION-LITE-DB3.BIN"
	defaultIPv6DB = "./resource/ipdb/IP2LOCATION-LITE-DB3.IPV6.BIN"
)

func init() {
	//ctx := gctx.New()
	//cfg := g.Cfg()
	//if cfg == nil {
	//	ReloadDB(ctx, defaultIPv4DB, defaultIPv6DB)
	//	return
	//}
	//ipv4Db := cfg.MustGet(ctx, "ipdb.ipv4")
	//ipv6Db := cfg.MustGet(ctx, "ipdb.ipv6")
	//
	//g.Log().Infof(ctx, "Init ipdb IP2LOCATION-LITE-DB3 ipv4:%s, ipv6:%s", ipv4Db, ipv6Db)
	//ReloadDB(ctx, ipv4Db.String(), ipv6Db.String())

}

// ReloadDB 使用数据库文件路径重新加载。
// param: 分别是v4 v6两个数据库的文件路径。如果只更新其中一个，把另一个传空字符串即可。
func ReloadDB(ctx context.Context, ipv4Addr, ipv6Addr string) {
	if len(ipv4Addr) > 0 {
		i4db, err := ip2location.OpenDB(ipv4Addr)
		if err != nil {
			g.Log().Errorf(ctx, "Init ipdb IP2LOCATION-LITE-DB3 OpenDB ipv4 failed! err:%s", err)
			return
		}
		ipv4db = i4db
	} else {
		g.Log().Errorf(ctx, "Init ipdb IP2LOCATION-LITE-DB3 OpenDB ipv4 path is empty! ")
	}
	if len(ipv6Addr) > 0 {
		i6db, err := ip2location.OpenDB(ipv6Addr)
		if err != nil {
			g.Log().Errorf(ctx, "Init ipdb IP2LOCATION-LITE-DB3 OpenDB ipv6 failed! err:%s", err)
			return
		}
		ipv6db = i6db
	} else {
		g.Log().Errorf(ctx, "Init ipdb IP2LOCATION-LITE-DB3 OpenDB ipv6 path is empty! ")
	}
}

// countryCode: 返回的国家码是2位，即 ISO 3166-1 alpha-2, 如果需要用三位请自行转换
func IPv4Loc(ip string) (countryCode, city string) {
	//record, _ := ipv4db.Get_all(ip)
	//return record.Country_short, record.City
	return "", ""
}

func IPv6Loc(ip string) (countryCode, city string) {
	//record, _ := ipv6db.Get_all(ip)
	//return record.Country_short, record.City
	return "", ""
}

// IPLoc 自动识别ipv4/ipv6，再查它的地址
func IPLoc(ip string) (countryCode, city string) {
	ipInst := net.ParseIP(ip)
	if ipInst == nil { // ip format invalid
		return "", ""
	}
	if ipInst.To4() != nil {
		return IPv4Loc(ip)
	} else {
		return IPv6Loc(ip)
	}
}

func IPRegion(ip string) (region string) {
	ipInst := net.ParseIP(ip)
	if ipInst == nil { // ip format invalid
		return ""
	}
	if ipInst.To4() != nil {
		record, _ := ipv4db.Get_all(ip)
		return record.Region
	} else {
		record, _ := ipv6db.Get_all(ip)
		return record.Region
	}
}
