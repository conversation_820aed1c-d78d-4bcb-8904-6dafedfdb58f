package cmd

import (
	"context"
	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	"halalplus/app/islamic-content-svc/internal/controller/islamic"
	"halalplus/utility"
	"halalplus/utility/gf-registry-consul"
	"halalplus/utility/unary"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "islamic-content-svc",
		Usage: "islamic-content-svc",
		Brief: "start islamic-content-svc grpc server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			registry, err := consul.New(consul.WithAddress("127.0.0.1:8500"))
			if err != nil {
				g.Log().Fatal(context.Background(), err)
			}

			grpcx.Resolver.Register(registry)
			c := grpcx.Server.NewConfig()
			c.Name = "islamic-content-svc"
			if g.IsEmpty(c.Address) {
				c.Address = utility.GetLocalLANIP() + ":0"
			}
			c.Options = append(c.Options, []grpc.ServerOption{
				grpcx.Server.ChainUnary(
					grpcx.Server.UnaryTracing,
					unary.UnaryLogRequestResponse,
					unary.UnaryI18n,
					unary.UnaryCommonError,
					grpcx.Server.UnaryValidate,
				)}...,
			)
			s := grpcx.Server.New(c)

			// 注册grpc的controller
			islamic.Register(s)

			// NOTICE: 一行代码，注册反射服务
			reflection.Register(s.Server)

			s.Run()
			return nil
		},
	}
)
