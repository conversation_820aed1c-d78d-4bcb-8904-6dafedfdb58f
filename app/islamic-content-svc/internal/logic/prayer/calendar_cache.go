package prayer

import (
	"context"
	"fmt"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

const (
	cacheRefreshInterval = time.Hour * 6
	cacheMaxYears        = 3
)

// HijriahCache 缓存管理器
type HijriahCache struct {
	// data 存储缓存数据，key格式: "year:methodCode"，value为该年的所有日期数据
	data map[string]map[string]*entity.CalendarHijriah // year:methodCode -> "month-day" -> CalendarHijriah
	mu   sync.RWMutex

	// 控制
	stopChan chan struct{}
	started  bool
}

// start 启动缓存管理器
func (c *HijriahCache) start() {
	if c.started {
		return
	}
	c.started = true

	// 初始加载数据
	c.loadInitialData()

	// 启动定时刷新
	ticker := time.NewTicker(cacheRefreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.refreshCache()
		case <-c.stopChan:
			return
		}
	}
}

// loadInitialData 初始加载数据
func (c *HijriahCache) loadInitialData() {
	ctx := context.Background()
	currentYear := time.Now().Year()

	// 加载当前年及前后几年的数据
	startYear := currentYear - cacheMaxYears
	endYear := currentYear + cacheMaxYears

	for year := startYear; year <= endYear; year++ {
		c.loadYearData(ctx, year)
	}

	g.Log().Info(ctx, fmt.Sprintf("HijriahCache初始化完成，已加载%d-%d年的数据", startYear, endYear))
}

// loadYearData 加载指定年份的数据
func (c *HijriahCache) loadYearData(ctx context.Context, year int) {
	// 查询该年的所有数据
	var hijriahDataList []*entity.CalendarHijriah
	err := dao.CalendarHijriah.Ctx(ctx).
		Where(hijriahCl.GregorianYear, year).
		Scan(&hijriahDataList)

	if err != nil {
		g.Log().Error(ctx, fmt.Sprintf("加载%d年Hijriah数据失败: %v", year, err))
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 按年份和方法代码分组存储
	for _, data := range hijriahDataList {
		yearMethodKey := fmt.Sprintf("%d:%s", year, data.MethodCode)
		if c.data[yearMethodKey] == nil {
			c.data[yearMethodKey] = make(map[string]*entity.CalendarHijriah)
		}

		dateKey := fmt.Sprintf("%d-%d", data.GregorianMonth, data.GregorianDay)
		c.data[yearMethodKey][dateKey] = data
	}

	// g.Log().Debug(ctx, fmt.Sprintf("已加载%d年的%d条Hijriah数据", year, len(hijriahDataList)))
}

// refreshCache 刷新缓存
func (c *HijriahCache) refreshCache() {
	ctx := context.Background()
	currentYear := time.Now().Year()

	// 清理过期数据
	c.cleanExpiredData(currentYear)

	// 加载新数据
	startYear := currentYear - cacheMaxYears
	endYear := currentYear + cacheMaxYears

	for year := startYear; year <= endYear; year++ {
		c.loadYearData(ctx, year)
	}

	g.Log().Debug(ctx, "HijriahCache刷新完成")
}

// cleanExpiredData 清理过期数据
func (c *HijriahCache) cleanExpiredData(currentYear int) {
	c.mu.Lock()
	defer c.mu.Unlock()

	minYear := currentYear - cacheMaxYears
	maxYear := currentYear + cacheMaxYears

	for key := range c.data {
		// 解析年份
		var year int
		var methodCode string
		fmt.Sscanf(key, "%d:%s", &year, &methodCode)

		if year < minYear || year > maxYear {
			delete(c.data, key)
		}
	}
}

// getFromCache 从缓存获取数据
func (c *HijriahCache) getFromCache(year int, month int, day int, methodCode string) (entity.CalendarHijriah, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	yearMethodKey := fmt.Sprintf("%d:%s", year, methodCode)
	yearData, exists := c.data[yearMethodKey]
	if !exists {
		return entity.CalendarHijriah{}, false
	}

	dateKey := fmt.Sprintf("%d-%d", month, day)
	return *yearData[dateKey], true
}

// getMonthFromCache 从缓存获取整月数据
func (c *HijriahCache) getMonthFromCache(year int, month int, methodCode string) map[int]entity.CalendarHijriah {
	c.mu.RLock()
	defer c.mu.RUnlock()

	yearMethodKey := fmt.Sprintf("%d:%s", year, methodCode)
	yearData, exists := c.data[yearMethodKey]
	if !exists {
		return nil
	}

	result := make(map[int]entity.CalendarHijriah)
	for dateKey, data := range yearData {
		var m, d int
		fmt.Sscanf(dateKey, "%d-%d", &m, &d)
		if m == month {
			result[d] = *data
		}
	}

	return result
}
