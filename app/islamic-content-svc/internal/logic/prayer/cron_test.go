package prayer

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/os/gcron"
)

func TestGoFrameCronScheduler(t *testing.T) {
	// 测试 GoFrame 定时器的基本功能
	cronName := "test_cron_job"

	// 创建定时任务
	_, err := gcron.Add(context.Background(), "* * * * * *", func(ctx context.Context) {
		// 测试用的空函数
	}, cronName)

	if err != nil {
		t.<PERSON><PERSON>("Failed to create cron job: %v", err)
	}

	// 验证定时任务是否存在
	entries := gcron.Entries()
	found := false
	for _, entry := range entries {
		if entry.Name == cronName {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected cron job to be created")
	}

	// 移除定时任务
	gcron.Remove(cronName)

	// 验证定时任务是否已移除
	entries = gcron.Entries()
	found = false
	for _, entry := range entries {
		if entry.Name == cronName {
			found = true
			break
		}
	}
	if found {
		t.Error("Expected cron job to be removed")
	}
}

func TestEventTypeConstants(t *testing.T) {
	// 验证事件类型常量
	expectedTypes := []string{
		"annualGregorian",
		"annualHijri",
		"monthlyHijri",
		"once",
		"weeklyGregorian",
	}

	actualTypes := []string{
		EventTypeAnnualGregorian,
		EventTypeAnnualHijri,
		EventTypeMonthlyHijri,
		EventTypeOnce,
		EventTypeWeeklyGregorian,
	}

	for i, expected := range expectedTypes {
		if actualTypes[i] != expected {
			t.Errorf("Expected event type %s, got %s", expected, actualTypes[i])
		}
	}
}

func TestFastingForbiddenDateLogic(t *testing.T) {
	// 创建一个简单的缓存实例来测试斋戒禁止日期逻辑
	cache := &EventConfigCache{}

	// 测试开斋节
	if !cache.isFastingForbiddenDate(10, 1) {
		t.Error("Expected Eid al-Fitr (10/1) to be forbidden for fasting")
	}

	// 测试古尔邦节
	if !cache.isFastingForbiddenDate(12, 10) {
		t.Error("Expected Eid al-Adha (12/10) to be forbidden for fasting")
	}

	// 测试塔什里克日
	if !cache.isFastingForbiddenDate(12, 11) {
		t.Error("Expected Ayyam al-Tashriq day 1 (12/11) to be forbidden for fasting")
	}
	if !cache.isFastingForbiddenDate(12, 12) {
		t.Error("Expected Ayyam al-Tashriq day 2 (12/12) to be forbidden for fasting")
	}
	if !cache.isFastingForbiddenDate(12, 13) {
		t.Error("Expected Ayyam al-Tashriq day 3 (12/13) to be forbidden for fasting")
	}

	// 测试正常日期
	if cache.isFastingForbiddenDate(12, 9) {
		t.Error("Expected normal date (12/9) to allow fasting")
	}
	if cache.isFastingForbiddenDate(1, 1) {
		t.Error("Expected normal date (1/1) to allow fasting")
	}
}
