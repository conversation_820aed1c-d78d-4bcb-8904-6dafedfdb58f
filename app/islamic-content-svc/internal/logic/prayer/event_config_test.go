package prayer

import (
	"context"
	"testing"

	"halalplus/app/islamic-content-svc/internal/model/entity"

	"github.com/gogf/gf/v2/os/gcron"
)

func TestEventConfigCache_CronScheduler(t *testing.T) {
	// 创建测试用的事件配置缓存
	cache := &EventConfigCache{
		configsByType: make(map[string][]*entity.CalendarEventConfig),
	}

	// 测试定时器创建（不调用 start，避免数据库连接）
	cronName := "test_event_config_refresh"
	cache.cronName = cronName

	// 手动添加定时任务进行测试
	_, err := gcron.Add(context.Background(), "0 0 * * * *", func(ctx context.Context) {
		// 测试用的空函数
	}, cronName)

	if err != nil {
		t.Errorf("Failed to create cron job: %v", err)
	}

	// 验证定时任务是否存在
	entries := gcron.Entries()
	found := false
	for _, entry := range entries {
		if entry.Name == cronName {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected cron job to be created")
	}

	// 测试停止功能
	cache.stop()

	// 验证定时任务是否已移除
	entries = gcron.Entries()
	found = false
	for _, entry := range entries {
		if entry.Name == cronName {
			found = true
			break
		}
	}
	if found {
		t.Error("Expected cron job to be removed")
	}

	if cache.cronName != "" {
		t.Error("Expected cron name to be cleared")
	}
}

func TestEventConfigCache_LoadConfigs(t *testing.T) {
	// 创建测试用的事件配置缓存
	cache := &EventConfigCache{
		configsByType: make(map[string][]*entity.CalendarEventConfig),
	}

	// 测试加载配置（这里会尝试连接数据库，在测试环境中可能失败，但不影响逻辑测试）
	cache.loadEventConfigs()

	// 验证配置映射已初始化
	if cache.configsByType == nil {
		t.Error("Expected configsByType to be initialized")
	}
}

func TestEventConfigCache_FastingFilter(t *testing.T) {
	// 创建测试用的事件配置缓存
	cache := &EventConfigCache{
		configsByType: make(map[string][]*entity.CalendarEventConfig),
	}

	// 添加斋戒事件配置
	cache.configsByType[EventTypeWeeklyGregorian] = []*entity.CalendarEventConfig{
		{
			Id:            1,
			Title:         "周一斋戒",
			EventType:     EventTypeWeeklyGregorian,
			EventCategory: "fasting",
			Weekday:       1, // Monday
		},
	}

	ctx := context.Background()

	// 测试开斋节（伊斯兰历10月1日）- 应该过滤掉斋戒事件
	events := cache.getEventsForDate(ctx, 2024, 4, 10, 1445, 10, 1, 1) // 假设这天是周一
	// 虽然是周一，但因为是开斋节，斋戒事件应该被过滤掉
	for _, event := range events {
		if event.EventType == "PUASA" {
			t.Error("Fasting event should be filtered out on Eid al-Fitr (10/1)")
		}
	}

	// 测试正常日期（伊斯兰历12月9日）- 斋戒事件应该正常显示
	events = cache.getEventsForDate(ctx, 2024, 6, 16, 1445, 12, 9, 1) // 假设这天是周一
	foundMondayFasting := false
	for _, event := range events {
		if event.EventType == "PUASA" && event.Title == "周一斋戒" {
			foundMondayFasting = true
		}
	}
	if !foundMondayFasting {
		t.Error("Monday fasting event should be present on normal dates")
	}
}

func TestEventTypes(t *testing.T) {
	// 验证事件类型常量
	expectedTypes := []string{
		"annualGregorian",
		"annualHijri",
		"monthlyHijri",
		"once",
		"weeklyGregorian",
	}

	actualTypes := []string{
		EventTypeAnnualGregorian,
		EventTypeAnnualHijri,
		EventTypeMonthlyHijri,
		EventTypeOnce,
		EventTypeWeeklyGregorian,
	}

	for i, expected := range expectedTypes {
		if actualTypes[i] != expected {
			t.Errorf("Expected event type %s, got %s", expected, actualTypes[i])
		}
	}
}

func TestIsFastingForbiddenDate(t *testing.T) {
	cache := &EventConfigCache{}

	// 测试开斋节
	if !cache.isFastingForbiddenDate(10, 1) {
		t.Error("Expected Eid al-Fitr (10/1) to be forbidden for fasting")
	}

	// 测试古尔邦节
	if !cache.isFastingForbiddenDate(12, 10) {
		t.Error("Expected Eid al-Adha (12/10) to be forbidden for fasting")
	}

	// 测试塔什里克日
	if !cache.isFastingForbiddenDate(12, 11) {
		t.Error("Expected Ayyam al-Tashriq day 1 (12/11) to be forbidden for fasting")
	}
	if !cache.isFastingForbiddenDate(12, 12) {
		t.Error("Expected Ayyam al-Tashriq day 2 (12/12) to be forbidden for fasting")
	}
	if !cache.isFastingForbiddenDate(12, 13) {
		t.Error("Expected Ayyam al-Tashriq day 3 (12/13) to be forbidden for fasting")
	}

	// 测试正常日期
	if cache.isFastingForbiddenDate(12, 9) {
		t.Error("Expected normal date (12/9) to allow fasting")
	}
	if cache.isFastingForbiddenDate(1, 1) {
		t.Error("Expected normal date (1/1) to allow fasting")
	}
}
