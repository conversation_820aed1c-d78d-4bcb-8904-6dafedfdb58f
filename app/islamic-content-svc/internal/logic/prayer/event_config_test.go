package prayer

import (
	"context"
	"testing"

	"halalplus/app/islamic-content-svc/internal/model/entity"
)

func TestEventConfigCache_getEventsForDate(t *testing.T) {
	// 创建测试用的事件配置缓存
	cache := &EventConfigCache{
		configsByType: make(map[string][]*entity.CalendarEventConfig),
		stopChan:      make(chan struct{}),
	}

	// 添加测试数据
	cache.configsByType[EventTypeAnnualGregorian] = []*entity.CalendarEventConfig{
		{
			Id:            1,
			Title:         "新年",
			EventType:     EventTypeAnnualGregorian,
			EventCategory: "holiday",
			Month:         1,
			Day:           1,
			Route:         "/holiday/new-year",
		},
	}

	cache.configsByType[EventTypeWeeklyGregorian] = []*entity.CalendarEventConfig{
		{
			Id:            2,
			Title:         "周一斋戒",
			EventType:     EventTypeWeeklyGregorian,
			EventCategory: "fasting",
			Weekday:       1, // Monday
			Route:         "/fasting/monday",
		},
	}

	cache.configsByType[EventTypeAnnualHijri] = []*entity.CalendarEventConfig{
		{
			Id:            3,
			Title:         "伊斯兰新年",
			EventType:     EventTypeAnnualHijri,
			EventCategory: "holiday",
			Month:         1,
			Day:           1,
			Route:         "/holiday/islamic-new-year",
		},
	}

	cache.configsByType[EventTypeMonthlyHijri] = []*entity.CalendarEventConfig{
		{
			Id:            4,
			Title:         "月中斋戒",
			EventType:     EventTypeMonthlyHijri,
			EventCategory: "fasting",
			Day:           15,
			Route:         "/fasting/monthly",
		},
	}

	cache.configsByType[EventTypeOnce] = []*entity.CalendarEventConfig{
		{
			Id:            5,
			Title:         "特殊会议",
			EventType:     EventTypeOnce,
			EventCategory: "meeting",
			Year:          2024,
			Month:         1,
			Day:           15,
			Route:         "/meeting/special",
		},
	}

	ctx := context.Background()

	// 测试用例1: 公历新年 (2024-01-01, 周一)
	events := cache.getEventsForDate(ctx, 2024, 1, 1, 1445, 6, 19, 1)
	if len(events) != 2 { // 应该有新年和周一斋戒两个事件
		t.Errorf("Expected 2 events for 2024-01-01 (Monday), got %d", len(events))
	}

	// 验证事件内容
	foundNewYear := false
	foundMondayFasting := false
	for _, event := range events {
		if event.Title == "新年" {
			foundNewYear = true
		}
		if event.Title == "周一斋戒" {
			foundMondayFasting = true
		}
	}
	if !foundNewYear {
		t.Error("Expected to find New Year event")
	}
	if !foundMondayFasting {
		t.Error("Expected to find Monday fasting event")
	}

	// 测试用例2: 伊斯兰新年 (假设某个公历日期对应伊斯兰历1月1日)
	events = cache.getEventsForDate(ctx, 2024, 7, 7, 1446, 1, 1, 0)
	foundIslamicNewYear := false
	for _, event := range events {
		if event.Title == "伊斯兰新年" {
			foundIslamicNewYear = true
		}
	}
	if !foundIslamicNewYear {
		t.Error("Expected to find Islamic New Year event")
	}

	// 测试用例3: 月中斋戒 (假设某个公历日期对应伊斯兰历某月15日)
	events = cache.getEventsForDate(ctx, 2024, 3, 25, 1445, 9, 15, 2)
	foundMonthlyFasting := false
	for _, event := range events {
		if event.Title == "月中斋戒" {
			foundMonthlyFasting = true
		}
	}
	if !foundMonthlyFasting {
		t.Error("Expected to find monthly fasting event")
	}

	// 测试用例4: 一次性事件
	events = cache.getEventsForDate(ctx, 2024, 1, 15, 1445, 6, 4, 1)
	foundSpecialMeeting := false
	for _, event := range events {
		if event.Title == "特殊会议" {
			foundSpecialMeeting = true
		}
	}
	if !foundSpecialMeeting {
		t.Error("Expected to find special meeting event")
	}

	// 测试用例5: 没有事件的日期
	events = cache.getEventsForDate(ctx, 2024, 6, 10, 1445, 12, 3, 1)
	// 这个日期应该只有周一斋戒
	if len(events) != 1 {
		t.Errorf("Expected 1 event for 2024-06-10 (Monday), got %d", len(events))
	}
}

func TestFastingForbiddenDates(t *testing.T) {
	// 创建测试用的事件配置缓存
	cache := &EventConfigCache{
		configsByType: make(map[string][]*entity.CalendarEventConfig),
		stopChan:      make(chan struct{}),
	}

	// 添加斋戒事件配置
	cache.configsByType[EventTypeWeeklyGregorian] = []*entity.CalendarEventConfig{
		{
			Id:            1,
			Title:         "周一斋戒",
			EventType:     EventTypeWeeklyGregorian,
			EventCategory: "fasting",
			Weekday:       1, // Monday
		},
	}

	cache.configsByType[EventTypeMonthlyHijri] = []*entity.CalendarEventConfig{
		{
			Id:            2,
			Title:         "月中斋戒",
			EventType:     EventTypeMonthlyHijri,
			EventCategory: "fasting",
			Day:           10, // 伊斯兰历每月10日
		},
	}

	ctx := context.Background()

	// 测试开斋节（伊斯兰历10月1日）- 应该过滤掉斋戒事件
	events := cache.getEventsForDate(ctx, 2024, 4, 10, 1445, 10, 1, 1) // 假设这天是周一
	// 虽然是周一，但因为是开斋节，斋戒事件应该被过滤掉
	for _, event := range events {
		if event.EventType == "PUASA" {
			t.Error("Fasting event should be filtered out on Eid al-Fitr (10/1)")
		}
	}

	// 测试古尔邦节（伊斯兰历12月10日）- 应该过滤掉斋戒事件
	events = cache.getEventsForDate(ctx, 2024, 6, 17, 1445, 12, 10, 1) // 假设这天是周一
	// 虽然是周一且是伊斯兰历12月10日，但因为是古尔邦节，斋戒事件应该被过滤掉
	for _, event := range events {
		if event.EventType == "PUASA" {
			t.Error("Fasting event should be filtered out on Eid al-Adha (12/10)")
		}
	}

	// 测试塔什里克日（伊斯兰历12月11日）- 应该过滤掉斋戒事件
	events = cache.getEventsForDate(ctx, 2024, 6, 18, 1445, 12, 11, 1) // 假设这天是周一
	for _, event := range events {
		if event.EventType == "PUASA" {
			t.Error("Fasting event should be filtered out on Ayyam al-Tashriq (12/11)")
		}
	}

	// 测试塔什里克日（伊斯兰历12月12日）- 应该过滤掉斋戒事件
	events = cache.getEventsForDate(ctx, 2024, 6, 19, 1445, 12, 12, 1) // 假设这天是周一
	for _, event := range events {
		if event.EventType == "PUASA" {
			t.Error("Fasting event should be filtered out on Ayyam al-Tashriq (12/12)")
		}
	}

	// 测试塔什里克日（伊斯兰历12月13日）- 应该过滤掉斋戒事件
	events = cache.getEventsForDate(ctx, 2024, 6, 20, 1445, 12, 13, 1) // 假设这天是周一
	for _, event := range events {
		if event.EventType == "PUASA" {
			t.Error("Fasting event should be filtered out on Ayyam al-Tashriq (12/13)")
		}
	}

	// 测试正常日期（伊斯兰历12月9日）- 斋戒事件应该正常显示
	events = cache.getEventsForDate(ctx, 2024, 6, 16, 1445, 12, 9, 1) // 假设这天是周一
	foundMondayFasting := false
	for _, event := range events {
		if event.EventType == "PUASA" && event.Title == "周一斋戒" {
			foundMondayFasting = true
		}
	}
	if !foundMondayFasting {
		t.Error("Monday fasting event should be present on normal dates")
	}
}

func TestIsFastingForbiddenDate(t *testing.T) {
	cache := &EventConfigCache{}

	// 测试开斋节
	if !cache.isFastingForbiddenDate(10, 1) {
		t.Error("Expected Eid al-Fitr (10/1) to be forbidden for fasting")
	}

	// 测试古尔邦节
	if !cache.isFastingForbiddenDate(12, 10) {
		t.Error("Expected Eid al-Adha (12/10) to be forbidden for fasting")
	}

	// 测试塔什里克日
	if !cache.isFastingForbiddenDate(12, 11) {
		t.Error("Expected Ayyam al-Tashriq day 1 (12/11) to be forbidden for fasting")
	}
	if !cache.isFastingForbiddenDate(12, 12) {
		t.Error("Expected Ayyam al-Tashriq day 2 (12/12) to be forbidden for fasting")
	}
	if !cache.isFastingForbiddenDate(12, 13) {
		t.Error("Expected Ayyam al-Tashriq day 3 (12/13) to be forbidden for fasting")
	}

	// 测试正常日期
	if cache.isFastingForbiddenDate(12, 9) {
		t.Error("Expected normal date (12/9) to allow fasting")
	}
	if cache.isFastingForbiddenDate(1, 1) {
		t.Error("Expected normal date (1/1) to allow fasting")
	}
}

func TestEventTypes(t *testing.T) {
	// 验证事件类型常量
	expectedTypes := []string{
		"annualGregorian",
		"annualHijri",
		"monthlyHijri",
		"once",
		"weeklyGregorian",
	}

	actualTypes := []string{
		EventTypeAnnualGregorian,
		EventTypeAnnualHijri,
		EventTypeMonthlyHijri,
		EventTypeOnce,
		EventTypeWeeklyGregorian,
	}

	for i, expected := range expectedTypes {
		if actualTypes[i] != expected {
			t.Errorf("Expected event type %s, got %s", expected, actualTypes[i])
		}
	}
}
