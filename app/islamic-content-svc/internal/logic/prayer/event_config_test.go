package prayer

import (
	"context"
	"testing"

	"halalplus/app/islamic-content-svc/internal/model/entity"
)

func TestEventConfigCache_getEventsForDate(t *testing.T) {
	// 创建测试用的事件配置缓存
	cache := &EventConfigCache{
		configsByType: make(map[string][]*entity.CalendarEventConfig),
		stopChan:      make(chan struct{}),
	}

	// 添加测试数据
	cache.configsByType[EventTypeAnnualGregorian] = []*entity.CalendarEventConfig{
		{
			Id:            1,
			Title:         "新年",
			EventType:     EventTypeAnnualGregorian,
			EventCategory: "holiday",
			Month:         1,
			Day:           1,
			Route:         "/holiday/new-year",
		},
	}

	cache.configsByType[EventTypeWeeklyGregorian] = []*entity.CalendarEventConfig{
		{
			Id:            2,
			Title:         "周一斋戒",
			EventType:     EventTypeWeeklyGregorian,
			EventCategory: "fasting",
			Weekday:       1, // Monday
			Route:         "/fasting/monday",
		},
	}

	cache.configsByType[EventTypeAnnualHijri] = []*entity.CalendarEventConfig{
		{
			Id:            3,
			Title:         "伊斯兰新年",
			EventType:     EventTypeAnnualHijri,
			EventCategory: "holiday",
			Month:         1,
			Day:           1,
			Route:         "/holiday/islamic-new-year",
		},
	}

	cache.configsByType[EventTypeMonthlyHijri] = []*entity.CalendarEventConfig{
		{
			Id:            4,
			Title:         "月中斋戒",
			EventType:     EventTypeMonthlyHijri,
			EventCategory: "fasting",
			Day:           15,
			Route:         "/fasting/monthly",
		},
	}

	cache.configsByType[EventTypeOnce] = []*entity.CalendarEventConfig{
		{
			Id:            5,
			Title:         "特殊会议",
			EventType:     EventTypeOnce,
			EventCategory: "meeting",
			Year:          2024,
			Month:         1,
			Day:           15,
			Route:         "/meeting/special",
		},
	}

	ctx := context.Background()

	// 测试用例1: 公历新年 (2024-01-01, 周一)
	events := cache.getEventsForDate(ctx, 2024, 1, 1, 1445, 6, 19, 1)
	if len(events) != 2 { // 应该有新年和周一斋戒两个事件
		t.Errorf("Expected 2 events for 2024-01-01 (Monday), got %d", len(events))
	}

	// 验证事件内容
	foundNewYear := false
	foundMondayFasting := false
	for _, event := range events {
		if event.Title == "新年" {
			foundNewYear = true
		}
		if event.Title == "周一斋戒" {
			foundMondayFasting = true
		}
	}
	if !foundNewYear {
		t.Error("Expected to find New Year event")
	}
	if !foundMondayFasting {
		t.Error("Expected to find Monday fasting event")
	}

	// 测试用例2: 伊斯兰新年 (假设某个公历日期对应伊斯兰历1月1日)
	events = cache.getEventsForDate(ctx, 2024, 7, 7, 1446, 1, 1, 0)
	foundIslamicNewYear := false
	for _, event := range events {
		if event.Title == "伊斯兰新年" {
			foundIslamicNewYear = true
		}
	}
	if !foundIslamicNewYear {
		t.Error("Expected to find Islamic New Year event")
	}

	// 测试用例3: 月中斋戒 (假设某个公历日期对应伊斯兰历某月15日)
	events = cache.getEventsForDate(ctx, 2024, 3, 25, 1445, 9, 15, 2)
	foundMonthlyFasting := false
	for _, event := range events {
		if event.Title == "月中斋戒" {
			foundMonthlyFasting = true
		}
	}
	if !foundMonthlyFasting {
		t.Error("Expected to find monthly fasting event")
	}

	// 测试用例4: 一次性事件
	events = cache.getEventsForDate(ctx, 2024, 1, 15, 1445, 6, 4, 1)
	foundSpecialMeeting := false
	for _, event := range events {
		if event.Title == "特殊会议" {
			foundSpecialMeeting = true
		}
	}
	if !foundSpecialMeeting {
		t.Error("Expected to find special meeting event")
	}

	// 测试用例5: 没有事件的日期
	events = cache.getEventsForDate(ctx, 2024, 6, 10, 1445, 12, 3, 1)
	// 这个日期应该只有周一斋戒
	if len(events) != 1 {
		t.Errorf("Expected 1 event for 2024-06-10 (Monday), got %d", len(events))
	}
}

func TestEventTypes(t *testing.T) {
	// 验证事件类型常量
	expectedTypes := []string{
		"annualGregorian",
		"annualHijri",
		"monthlyHijri",
		"once",
		"weeklyGregorian",
	}

	actualTypes := []string{
		EventTypeAnnualGregorian,
		EventTypeAnnualHijri,
		EventTypeMonthlyHijri,
		EventTypeOnce,
		EventTypeWeeklyGregorian,
	}

	for i, expected := range expectedTypes {
		if actualTypes[i] != expected {
			t.Errorf("Expected event type %s, got %s", expected, actualTypes[i])
		}
	}
}
