package prayer

import (
	"context"
	"fmt"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// EventConfigCache 事件配置缓存管理器
type EventConfigCache struct {
	mu            sync.RWMutex
	configs       []*entity.CalendarEventConfig            // 所有配置
	configsByType map[string][]*entity.CalendarEventConfig // 按类型分组的配置
	started       bool
	stopChan      chan struct{}
}

// EventType 事件类型常量
const (
	EventTypeAnnualGregorian = "annualGregorian" // 每年公历固定日期
	EventTypeAnnualHijri     = "annualHijri"     // 每年伊斯兰历固定日期
	EventTypeMonthlyHijri    = "monthlyHijri"    // 每月伊斯兰历固定日期
	EventTypeOnce            = "once"            // 一次性事件
	EventTypeWeeklyGregorian = "weeklyGregorian" // 每周固定星期
)

// start 启动事件配置缓存管理器
func (c *EventConfigCache) start() {
	if c.started {
		return
	}
	c.started = true

	// 初始加载配置
	c.loadEventConfigs()

	// 启动定时刷新（每小时刷新一次，因为配置变动不频繁）
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.loadEventConfigs()
		case <-c.stopChan:
			return
		}
	}
}

// loadEventConfigs 加载事件配置
func (c *EventConfigCache) loadEventConfigs() {
	ctx := context.Background()

	var configs []*entity.CalendarEventConfig
	err := dao.CalendarEventConfig.Ctx(ctx).Scan(&configs)
	if err != nil {
		g.Log().Error(ctx, "加载事件配置失败:", err)
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	c.configs = configs
	c.configsByType = make(map[string][]*entity.CalendarEventConfig)

	// 按类型分组
	for _, config := range configs {
		c.configsByType[config.EventType] = append(c.configsByType[config.EventType], config)
	}

	g.Log().Info(ctx, fmt.Sprintf("事件配置缓存已更新，共加载%d条配置", len(configs)))
}

// getEventsForDate 根据日期获取事件
func (c *EventConfigCache) getEventsForDate(ctx context.Context, gregorianYear, gregorianMonth, gregorianDay int, hijriYear, hijriMonth, hijriDay int, weekday int) []*model.CalendarEventInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var events []*model.CalendarEventInfo

	// 检查是否为禁止斋戒的日子
	isFastingForbidden := c.isFastingForbiddenDate(hijriMonth, hijriDay)

	// 处理每年公历固定日期事件
	if configs, exists := c.configsByType[EventTypeAnnualGregorian]; exists {
		for _, config := range configs {
			if config.Month == gregorianMonth && config.Day == gregorianDay {
				// 如果是斋戒事件且在禁止斋戒的日子，则跳过
				if config.EventCategory == "fasting" && isFastingForbidden {
					continue
				}
				events = append(events, c.convertToEventInfo(config))
			}
		}
	}

	// 处理每年伊斯兰历固定日期事件
	if configs, exists := c.configsByType[EventTypeAnnualHijri]; exists {
		for _, config := range configs {
			if config.Month == hijriMonth && config.Day == hijriDay {
				// 如果是斋戒事件且在禁止斋戒的日子，则跳过
				if config.EventCategory == "fasting" && isFastingForbidden {
					continue
				}
				events = append(events, c.convertToEventInfo(config))
			}
		}
	}

	// 处理每月伊斯兰历固定日期事件
	if configs, exists := c.configsByType[EventTypeMonthlyHijri]; exists {
		for _, config := range configs {
			if config.Day == hijriDay {
				// 如果是斋戒事件且在禁止斋戒的日子，则跳过
				if config.EventCategory == "fasting" && isFastingForbidden {
					continue
				}
				events = append(events, c.convertToEventInfo(config))
			}
		}
	}

	// 处理一次性事件
	if configs, exists := c.configsByType[EventTypeOnce]; exists {
		for _, config := range configs {
			if config.Year == gregorianYear && config.Month == gregorianMonth && config.Day == gregorianDay {
				// 如果是斋戒事件且在禁止斋戒的日子，则跳过
				if config.EventCategory == "fasting" && isFastingForbidden {
					continue
				}
				events = append(events, c.convertToEventInfo(config))
			}
		}
	}

	// 处理每周固定星期事件
	if configs, exists := c.configsByType[EventTypeWeeklyGregorian]; exists {
		for _, config := range configs {
			if config.Weekday == weekday {
				// 如果是斋戒事件且在禁止斋戒的日子，则跳过
				if config.EventCategory == "fasting" && isFastingForbidden {
					continue
				}
				events = append(events, c.convertToEventInfo(config))
			}
		}
	}

	return events
}

// isFastingForbiddenDate 检查是否为禁止斋戒的日子
// 根据伊斯兰教法，以下日子禁止斋戒：
// 1. 开斋节（伊斯兰历10月1日）
// 2. 古尔邦节/宰牲节（伊斯兰历12月10日）
// 3. 塔什里克日（伊斯兰历12月11、12、13日）
func (c *EventConfigCache) isFastingForbiddenDate(hijriMonth, hijriDay int) bool {
	// 开斋节：伊斯兰历10月1日
	if hijriMonth == 10 && hijriDay == 1 {
		return true
	}

	// 古尔邦节/宰牲节：伊斯兰历12月10日
	if hijriMonth == 12 && hijriDay == 10 {
		return true
	}

	// 塔什里克日：伊斯兰历12月11、12、13日
	// 这三天是古尔邦节后的日子，也禁止斋戒
	if hijriMonth == 12 && (hijriDay == 11 || hijriDay == 12 || hijriDay == 13) {
		return true
	}

	return false
}

// convertToEventInfo 将配置转换为事件信息
func (c *EventConfigCache) convertToEventInfo(config *entity.CalendarEventConfig) *model.CalendarEventInfo {
	var eventType string
	switch config.EventCategory {
	case "holiday":
		eventType = "HARI_BESAR" // 前端会把这个标红点
	case "fasting":
		eventType = "PUASA"
	default:
		eventType = "LIBUR_NASIONAL"
	}

	return &model.CalendarEventInfo{
		Id:          int64(config.Id),
		EventType:   eventType,
		Title:       config.Title,
		Description: "", // 配置表中没有description字段，如果需要可以扩展
		JumpUrl:     "", // 暂时不支持
	}
}

// stop 停止事件配置缓存管理器
func (c *EventConfigCache) stop() {
	if !c.started {
		return
	}
	close(c.stopChan)
	c.started = false
}
