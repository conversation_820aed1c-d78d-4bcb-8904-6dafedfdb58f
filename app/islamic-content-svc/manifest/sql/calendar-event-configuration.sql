create table calendar_events
(
    id            int auto_increment
        primary key,
    title         varchar(255)                        not null comment '事件标题',
    eventType     varchar(50)                         not null comment '事件类型',
    eventCategory varchar(50)                         not null comment '事件分类',
    year          int                                 null comment '年份',
    month         int                                 null comment '月份',
    day           int                                 null comment '日期',
    weekday       int                                 null comment '星期几',
    route         varchar(255)                        null comment '路由路径',
    params        json                                null comment '参数对象',
    created_time    timestamp default CURRENT_TIMESTAMP null comment '创建时间'
)
    comment '日历事件表';

create index idx_date
    on test.calendar_events (year, month, day);

create index idx_event_category
    on test.calendar_events (eventCategory);

create index idx_event_type
    on test.calendar_events (eventType);

create index idx_weekday
    on test.calendar_events (weekday);

