create table calendar_event_config
(
    id            int auto_increment
        primary key,
    title         varchar(255)                        not null comment '事件标题',
    event_type    varchar(50)                         not null comment '事件类型',
    event_category varchar(50)                        not null comment '事件分类',
    year          int                                 null comment '年份',
    month         int                                 null comment '月份',
    day           int                                 null comment '日期',
    weekday       int                                 null comment '星期几',
    route         varchar(255)                        null comment '路由路径',
    params        json                                null comment '参数对象',
    created_time  BIGINT UNSIGNED NOT NULL DEFAULT 0 comment '创建时间'
)
    comment '日历事件配置表';

create index idx_date
    on calendar_event_config (year, month, day);

create index idx_event_category
    on calendar_event_config (event_category);

create index idx_event_type
    on calendar_event_config (event_type);

create index idx_weekday
    on calendar_event_config (weekday);


INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (1, '<PERSON><PERSON>', 'annualGregorian', 'holiday', null, 1, 1, null, null, null, 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (2, 'Hari lahir Pencak Silat Nahdlatul Ulama (PSNU) Pagar Nusa', 'annualGregorian', 'importantday', null, 1, 3, null, '/encyclopedia/encyclopedia-detail', '{"id": 310, "title": "Pagar Nusa"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (3, 'Hari lahir Jam\'iyyatul Qurra\' wal Huffadh Nahdlatul Ulama (JQHNU)', 'annualGregorian', 'importantday', null, 1, 15, null, '/encyclopedia/encyclopedia-detail', '{"id": 513, "title": "JQHNU"}', 0);

INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (53, 'Tahun Baru Hijriah', 'annualHijri', 'holiday', null, 1, 1, null, '/article/article-detail', '{"id": 137818}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (54, 'Puasa Tasu\'a', 'annualHijri', 'fasting', null, 1, 9, null, '/tutorial/tutorial-detail', '{"id": 31, "title": "Cara Puasa Bulan Muharram"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (55, 'Puasa Asyura', 'annualHijri', 'fasting', null, 1, 10, null, '/tutorial/tutorial-detail', '{"id": 31, "title": "Cara Puasa Bulan Muharram"}', 0);


INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (50, 'Puasa Sunnah Ayyamul Bidh', 'monthlyHijri', 'fasting', null, null, 13, null, '/tutorial/tutorial-detail', '{"id": 55, "title": "Cara Puasa Ayyamul Bidl"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (51, 'Puasa Sunnah Ayyamul Bidh', 'monthlyHijri', 'fasting', null, null, 14, null, '/tutorial/tutorial-detail', '{"id": 55, "title": "Cara Puasa Ayyamul Bidl"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (52, 'Puasa Sunnah Ayyamul Bidh', 'monthlyHijri', 'fasting', null, null, 15, null, '/article/article-detail', '{"id": 130441}', 0);


INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (44, 'Muktamar Ke-34 Nahdlatul Ulama', 'once', 'importantday', 2021, 12, 23, null, '/article/article-prefix', '{"prefix": "MUKTAMAR KE-34 NU"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (45, 'Muktamar Ke-34 Nahdlatul Ulama', 'once', 'importantday', 2021, 12, 24, null, '/article/article-prefix', '{"prefix": "MUKTAMAR KE-34 NU"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (46, 'Muktamar Ke-34 Nahdlatul Ulama', 'once', 'importantday', 2021, 12, 25, null, '/article/article-prefix', '{"prefix": "MUKTAMAR KE-34 NU"}', 0);


INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (95, 'Puasa Sunnah Senin', 'weeklyGregorian', 'fasting', null, null, null, 1, '/tutorial/tutorial-detail', '{"id": 53, "title": "Cara Puasa Senin-Kamis"}', 0);
INSERT INTO islamic_content_svc.calendar_event_config (id, title, event_type, event_category, year, month, day, weekday, route, params, create_time) VALUES (96, 'Puasa Sunnah Kamis', 'weeklyGregorian', 'fasting', null, null, null, 4, '/tutorial/tutorial-detail', '{"id": 53, "title": "Cara Puasa Senin-Kamis"}', 0);

