# 日历事件配置系统

## 概述

本系统实现了基于配置的日历事件管理，支持5种不同类型的事件配置，程序启动时自动加载配置并缓存，在查询日历时根据不同的事件类型进行智能匹配。

## 表结构

### calendar_event_config 表

```sql
create table calendar_event_config
(
    id            int auto_increment primary key,
    title         varchar(255) not null comment '事件标题',
    event_type    varchar(50)  not null comment '事件类型',
    event_category varchar(50) not null comment '事件分类',
    year          int          null comment '年份',
    month         int          null comment '月份',
    day           int          null comment '日期',
    weekday       int          null comment '星期几',
    route         varchar(255) null comment '路由路径',
    params        json         null comment '参数对象',
    create_time   BIGINT UNSIGNED NOT NULL DEFAULT 0 comment '创建时间'
) comment '日历事件配置表';
```

## 事件类型

### 1. annualGregorian - 每年公历固定日期
- **用途**: 每年在公历的固定日期重复的事件
- **配置字段**: `month`, `day`
- **示例**: 新年(1月1日)、国庆节等
- **匹配逻辑**: 当查询日期的公历月份和日期与配置匹配时显示

### 2. annualHijri - 每年伊斯兰历固定日期  
- **用途**: 每年在伊斯兰历的固定日期重复的事件
- **配置字段**: `month`, `day`
- **示例**: 伊斯兰新年(Muharram 1日)、阿舒拉节等
- **匹配逻辑**: 当查询日期的伊斯兰历月份和日期与配置匹配时显示

### 3. monthlyHijri - 每月伊斯兰历固定日期
- **用途**: 每个伊斯兰历月份的固定日期重复的事件
- **配置字段**: `day`
- **示例**: 每月15日的斋戒(Ayyamul Bidh)
- **匹配逻辑**: 当查询日期的伊斯兰历日期与配置匹配时显示

### 4. once - 一次性事件
- **用途**: 只在特定日期发生一次的事件
- **配置字段**: `year`, `month`, `day`
- **示例**: 会议、特殊活动等
- **匹配逻辑**: 当查询日期的公历年、月、日完全匹配时显示

### 5. weeklyGregorian - 每周固定星期
- **用途**: 每周在固定星期重复的事件
- **配置字段**: `weekday` (0=Sunday, 1=Monday, ..., 6=Saturday)
- **示例**: 周一周四斋戒
- **匹配逻辑**: 当查询日期的星期几与配置匹配时显示

## 系统架构

### 核心组件

1. **EventConfigCache**: 事件配置缓存管理器
   - 程序启动时加载所有配置
   - 按事件类型分组存储
   - 定时刷新缓存(每小时)

2. **事件匹配引擎**: 根据不同事件类型实现不同的匹配逻辑
   - 支持公历和伊斯兰历日期匹配
   - 支持星期匹配
   - 支持一次性和重复性事件

### 工作流程

1. **启动阶段**:
   ```go
   // 在 prayer.New() 中启动事件配置缓存
   eventCache := &EventConfigCache{...}
   go eventCache.start()
   ```

2. **查询阶段**:
   ```go
   // 在 getEventsByMonth() 中为每一天查询事件
   events := s.eventCache.getEventsForDate(ctx, year, month, day, 
       hijriYear, hijriMonth, hijriDay, weekday)
   ```

## 配置示例

### 公历年度事件
```sql
INSERT INTO calendar_event_config 
(title, event_type, event_category, month, day, route) 
VALUES ('新年', 'annualGregorian', 'holiday', 1, 1, '/holiday/new-year');
```

### 伊斯兰历年度事件
```sql
INSERT INTO calendar_event_config 
(title, event_type, event_category, month, day, route) 
VALUES ('伊斯兰新年', 'annualHijri', 'holiday', 1, 1, '/holiday/islamic-new-year');
```

### 每月伊斯兰历事件
```sql
INSERT INTO calendar_event_config 
(title, event_type, event_category, day, route) 
VALUES ('月中斋戒', 'monthlyHijri', 'fasting', 15, '/fasting/monthly');
```

### 一次性事件
```sql
INSERT INTO calendar_event_config 
(title, event_type, event_category, year, month, day, route) 
VALUES ('特殊会议', 'once', 'meeting', 2024, 12, 25, '/meeting/special');
```

### 每周事件
```sql
INSERT INTO calendar_event_config 
(title, event_type, event_category, weekday, route) 
VALUES ('周一斋戒', 'weeklyGregorian', 'fasting', 1, '/fasting/monday');
```

## 性能优化

1. **启动时加载**: 避免每次查询都访问数据库
2. **按类型分组**: 快速定位相关配置
3. **内存缓存**: 所有配置保存在内存中
4. **定时刷新**: 平衡性能和数据一致性

## 扩展性

系统设计支持轻松添加新的事件类型：
1. 在常量中定义新的事件类型
2. 在 `getEventsForDate` 方法中添加匹配逻辑
3. 更新相关测试用例

## 测试

运行测试验证功能：
```bash
go test ./internal/logic/prayer -v -run TestEventConfigCache
go test ./internal/logic/prayer -v -run TestEventTypes
```
