package libfile

import (
	"context"
	"fmt"
	"github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
	"io"
	"net/url"
	"time"
)

type ObsClient struct {
	client *obs.ObsClient
	config ObsS3Config
}

var _ S3Client = &ObsClient{}

const S3Obs = "obs"

// ObsS3Config aws s3的配置
type ObsS3Config struct {
	Endpoint        string `json:"endpoint"`
	AccessKeyID     string `json:"accessKeyID"`
	SecretAccessKey string `json:"secretAccessKey"`
	Bucket          string `json:"bucket"`
	Region          string `json:"region"`
	SessionToken    string `json:"sessionToken"`
	IsPublic        bool   `json:"isPublic"`
	CdnDomain       string `json:"cdnDomain"`
}

func NewObsS3Client(ctx context.Context, cfg ObsS3Config) (*ObsClient, error) {
	client, err := obs.New(cfg.AccessKeyID, cfg.SecretAccessKey, cfg.Endpoint)
	if err != nil {
		return nil, err
	}
	return &ObsClient{client: client, config: cfg}, nil
}

// GetObjectURL  获取对象的访问网址(只支持公开访问的)
func (c *ObsClient) GetObjectURL(ctx context.Context, bucketName, objectName string) *url.URL {
	u := url.URL{
		Scheme: "https",
		Host:   fmt.Sprintf("%s.%s", bucketName, c.config.Endpoint),
		Path:   fmt.Sprintf("/%s", objectName),
	}
	if c.config.CdnDomain != "" {
		u.Host = c.config.CdnDomain
	}
	return &u
}

// PutObject 上传对象(小文件)
func (c *ObsClient) PutObject(ctx context.Context, bucketName, objectName string, reader io.Reader) error {
	input := &obs.PutObjectInput{
		PutObjectBasicInput: obs.PutObjectBasicInput{
			ObjectOperationInput: obs.ObjectOperationInput{
				Bucket: bucketName,
				Key:    objectName,
			},
		},
		Body: reader,
	}
	_, err := c.client.PutObject(input)
	return err
}

// CopyObject 复制对象（同桶或跨桶）
func (c *ObsClient) CopyObject(ctx context.Context, bucketName, destObjectName, srcObjectName string) error {
	input := &obs.CopyObjectInput{
		ObjectOperationInput: obs.ObjectOperationInput{
			Bucket: bucketName,
			Key:    destObjectName,
		},
		CopySourceBucket: bucketName + "/" + srcObjectName,
	}
	_, err := c.client.CopyObject(input)
	return err
}

// RemoveObject 删除单个对象
func (c *ObsClient) RemoveObject(ctx context.Context, bucketName, objectName string) error {
	input := &obs.DeleteObjectInput{
		Bucket: bucketName,
		Key:    objectName,
	}
	_, err := c.client.DeleteObject(input)
	return err
}

// RemoveObjects 批量删除对象
func (c *ObsClient) RemoveObjects(ctx context.Context, bucketName string, objectNames []string) error {
	objects := make([]obs.ObjectToDelete, len(objectNames))
	for i, name := range objectNames {
		objects[i] = obs.ObjectToDelete{Key: name}
	}
	input := &obs.DeleteObjectsInput{
		Bucket:  bucketName,
		Objects: objects,
	}
	_, err := c.client.DeleteObjects(input)
	return err
}

// PresignedGetObject 生成带签名的临时下载 URL
func (c *ObsClient) PresignedGetObject(ctx context.Context, bucketName, objectName string, expires time.Duration) (*url.URL, error) {
	input := &obs.CreateSignedUrlInput{
		Method:  obs.HttpMethodGet,
		Bucket:  bucketName,
		Key:     objectName,
		Expires: int(expires.Seconds()),
	}
	out, err := c.client.CreateSignedUrl(input)
	if err != nil {
		return nil, err
	}
	return url.Parse(out.SignedUrl)
}
