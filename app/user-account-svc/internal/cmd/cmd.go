package cmd

import (
	"context"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	"halalplus/app/user-account-svc/internal/controller/addr"
	"halalplus/app/user-account-svc/internal/controller/data"
	"halalplus/app/user-account-svc/internal/controller/msg"
	"halalplus/app/user-account-svc/internal/controller/nu"
	"halalplus/app/user-account-svc/internal/controller/user"
	"halalplus/utility"

	"halalplus/utility/unary"
)

var (
	Main = gcmd.Command{
		Name:  "user-account-svc",
		Usage: "user-account-svc",
		Brief: "start user-account-svc grpc server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			c := grpcx.Server.NewConfig()
			c.Name = "user-account-svc"
			if g.IsEmpty(c.Address) {
				c.Address = utility.GetLocalLANIP() + ":0"
			}
			c.Options = append(c.Options, []grpc.ServerOption{
				grpcx.Server.ChainUnary(
					grpcx.Server.UnaryTracing,
					unary.UnaryLogRequestResponse,
					unary.UnaryI18n,
					unary.UnaryCommonError,
					grpcx.Server.UnaryValidate,
				)}...,
			)
			s := grpcx.Server.New(c)
			user.Register(s)
			data.Register(s)
			addr.Register(s)
			nu.Register(s)
			msg.Register(s)
			// NOTICE: 一行代码，注册反射服务
			reflection.Register(s.Server)

			s.Run()
			return nil
		},
	}
)
