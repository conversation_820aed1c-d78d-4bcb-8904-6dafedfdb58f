package errno

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

var (
	CodeUserNotFoundError               = gcode.New(20000, "user.not.found", nil)                         // 找不到用户
	CodeOptCodeError                    = gcode.New(20001, "account.opt.code.expired.incorrect", nil)     // 验证OPT失败
	CodeVerifyAccessTokenError          = gcode.New(20002, "account.access.token.expired.incorrect", nil) // 验证AccessToken失败
	CodeUserLoginBen                    = gcode.New(20003, "user.login.ban.time", nil)                    // 用户禁止登录
	CodeUserAccountExisted              = gcode.New(20004, "user.account.account.existed", nil)           // 用户账户已经存在
	CodeUserPhoneNumberExisted          = gcode.New(20005, "user.account.phoneNum.existed", nil)          // 用户手机号码已经存在
	CodeUserPhoneNumberNotExisted       = gcode.New(20007, "user.phone.number.not.existed", nil)          // 用户手机号码不存在
	CodeUserPhoneNumberInvalid          = gcode.New(20008, "user.phone.number.not.invalid", nil)
	CodePaymentPasswordError            = gcode.New(20009, "user.payment.password.error", nil)       // 验证支付密码错误失败
	CodeBankCardMaxLimit                = gcode.New(20010, "use.bankcard.max.limit.num", nil)        // 用户最多有N张银行卡
	CodeCoinAddressMaxLimit             = gcode.New(20010, "use.coinAddress.max.limit.num", nil)     // 用户最多有N虚拟币地址
	CodeUserBankCardAccountNumDuplicate = gcode.New(20011, "use.bankcard.accountNum.duplicate", nil) // 用户银行卡号重复
	CodeUserCoinAddressDuplicate        = gcode.New(20011, "use.coinAddress.duplicate", nil)         // 用户虚拟币地址号重复
	CodeUserPhoneNumberMustBeSet        = gcode.New(20012, "use.account.phoneNum.must.be.set", nil)  // 用户手机号码必须设置
	CodeUserBankCardIdNotFound          = gcode.New(20013, "use.bankcard.id.not.find", nil)          // 用户银行卡号不存在
	CodeUserCoinAddressIdNotFound       = gcode.New(20013, "use.coinAddress.id.not.find", nil)       // 用户虚拟币地址不存在
	CodeUserNameExisted                 = gcode.New(20014, "user.account.name.existed", nil)         // 用户姓名已经存在
	CodeUserAccountPasswordError        = gcode.New(20015, "user.account.password.error", nil)       // 用户姓名已经存在
	CodeInvalidEmailError               = gcode.New(20016, "user.email.error", nil)                  // 邮箱格式不正确
	CodeUserEmailExisted                = gcode.New(20017, "user.email.existed", nil)                // 邮箱格式不正确
	CodeUserInvalidParameter            = gcode.New(20018, "user.invalid.parameter", nil)            // 参数不正确
	CodeSendEmailError                  = gcode.New(20019, "send.email.error", nil)
	CodeSendOtpError                    = gcode.New(20020, "send.otp.error", nil)
	CodeOtpMustSend                     = gcode.New(20021, "otp.must.send", nil)
	CodeUserPasswordExisted             = gcode.New(20022, "user.password.existed", nil) // 已设置密码
	CodeUserPasswordInvalid             = gcode.New(20023, "user.password.invalid", nil) // 密码错误
	CodeUserPasswordError               = gcode.New(20024, "user.password.error", nil)   //  密码格式不符合要求
	CodeUserUnknownError                = gcode.New(20025, "user.unknown.error", nil)    //  未知错误
)
